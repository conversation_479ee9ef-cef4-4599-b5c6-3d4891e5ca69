# X CV Extractor

An AI-powered FastAPI microservice that intelligently extracts and structures data from CV/resume documents using advanced LLM technology. The service provides robust APIs for CV parsing with enterprise-grade features including caching, async processing, and comprehensive data storage.

## Overview

X CV Extractor is a production-ready document processing service that transforms unstructured CV/resume content into structured, searchable data. Built with modern Python technologies, it leverages OpenAI's GPT models through LangChain for intelligent text analysis and provides both synchronous and asynchronous processing capabilities.

### Key Capabilities
- **AI-Powered Extraction**: Uses OpenAI GPT models with specialized prompts for different CV sections (experience, education, skills, etc.)
- **Multi-Format Support**: Processes PDF, DOC, and DOCX files with automatic PII detection
- **Enterprise Features**: Redis caching, Elasticsearch storage, Kafka integration, and comprehensive error tracking
- **Dual API Design**: Public and internal endpoints for different use cases
- **Production Ready**: Includes health monitoring, load testing, and observability features

## Architecture & Features

### Core Components

#### 🤖 **LLM Processing Engine**
- **Parallel Prompt Processing**: Uses 5 specialized prompts simultaneously for different CV sections
- **Smart Retry Logic**: Automatic error recovery with GPT-4 fallback parsing
- **Content Cleaning**: Advanced text preprocessing and JSON sanitization
- **Response Caching**: Redis-based caching for improved performance and cost reduction

#### 📄 **Document Processing Pipeline**
- **Multi-Format Support**: PDF, DOC, DOCX with robust content extraction
- **PII Detection**: Automatic identification and handling of personal information (emails, phone numbers)
- **Phone Validation**: International phone number parsing and formatting using `phonenumbers`
- **File Size Validation**: Configurable limits with proper error handling

#### 🔌 **API Architecture**
- **Dual Endpoint Design**:
  - `/public/api/v1.0` - Public-facing endpoints
  - `/internal/api/v1.0` - Internal system integration
- **RESTful Design**: Standard HTTP methods with proper status codes
- **File Upload Support**: Multipart form data handling with async processing
- **Document Retrieval**: Direct access to processed documents by Elasticsearch ID

#### 🗄️ **Data Storage & Search**
- **Elasticsearch Integration**: Comprehensive field mapping for all CV components
- **Structured Data Model**: Standardized schema for experience, education, skills, etc.
- **Document Indexing**: Automatic indexing with unique ID generation
- **Search Capabilities**: Full-text search and structured queries

#### ⚡ **Async Processing**
- **Kafka Integration**: Message-driven architecture for background processing
- **Consumer/Producer Pattern**: Scalable async job processing
- **External File Processing**: S3-compatible file URL processing
- **Result Notification**: Automatic completion notifications via Kafka

#### 🔧 **Enterprise Features**
- **Health Monitoring**: Comprehensive health check endpoints
- **Error Tracking**: Sentry integration for production monitoring
- **CORS Support**: Configurable cross-origin resource sharing
- **Environment Management**: Flexible configuration for dev/prod environments
- **Observability**: Langfuse integration for LLM call tracking and analytics

## Installation

### Prerequisites

- **Python 3.11+** - Required runtime environment
- **Poetry** - Dependency management and virtual environment
- **Elasticsearch 8.x** - Document storage and search engine
- **Redis** - Caching layer for LLM responses (recommended)
- **Kafka** - Message broker for async processing (optional)
- **OpenAI API Key** - For LLM-powered CV parsing

### Setup

1. **Clone the repository**
   ```bash
   git clone https://git.navigosgroup.com/ai/x-cv-extractor.git
   cd x-cv-extractor
   ```

2. **Install dependencies using Poetry**
   ```bash
   poetry install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```bash
   # Application Configuration
   ENVIRONMENT=dev
   APP_HOST=0.0.0.0
   APP_PORT=8080
   APP_WORKERS=1

   # AI/LLM Configuration
   OPENAI_API_KEY=your_openai_api_key
   OPENAI_MODEL=gpt-4o-mini  # or gpt-4o for higher accuracy

   # Elasticsearch Configuration
   ES_HOST=localhost
   ES_PORT=9200
   ES_INDEX=x_cv_extractor
   ES_USER=  # if authentication required
   ES_PASSWORD=  # if authentication required

   # Redis Configuration (for caching)
   REDIS_URL=redis://localhost:6379
   REDIS_PASSWORD=  # if authentication required

   # Kafka Configuration (for async processing)
   BROKER_URL=localhost:9092
   CONSUMER_TOPIC=cv-processing-requests
   PRODUCER_TOPIC=cv-processing-results

   # External Storage
   S3_DOMAIN=https://your-s3-bucket.com

   # Monitoring & Observability
   SENTRY_DSN=your_sentry_dsn
   LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
   LANGFUSE_SECRET_KEY=your_langfuse_secret_key
   ```

4. **Run the application**
   ```bash
   # Using Poetry
   poetry run python main.py

   # Or activate virtual environment first
   poetry shell
   python main.py
   ```

5. **Access the API documentation**
   - Development: http://localhost:8080/docs (automatically redirects from root)
   - Swagger UI: http://localhost:8080/docs
   - ReDoc: http://localhost:8080/redoc

## Project Structure

```
x-cv-extractor/
├── app/                          # Main application package
│   ├── __init__.py
│   ├── api/                      # API layer
│   │   ├── __init__.py
│   │   └── v1/                   # API version 1
│   │       ├── __init__.py
│   │       └── routers/          # API route handlers
│   │           ├── __init__.py
│   │           ├── extractor.py  # CV extraction endpoints
│   │           └── health.py     # Health check endpoints
│   ├── consumer/                 # Kafka consumers and sync services
│   │   ├── consumer.py           # Main Kafka consumer
│   │   └── sync-profile.py       # Profile synchronization service
│   ├── handlers/                 # Business logic handlers
│   │   ├── __init__.py
│   │   ├── file_processing.py    # File upload and processing
│   │   ├── llm.py               # LLM integration and processing
│   │   └── prompts.py           # AI prompts and templates
│   └── models/                   # Data models and schemas
│       ├── __init__.py
│       └── user_profile.py       # User profile data models
├── logs/                         # Application logs
│   └── parsed_fields.json        # Parsed field logs
├── uploads/                      # Temporary file uploads
├── main.py                       # Application entry point
├── pyproject.toml               # Poetry configuration and dependencies
├── poetry.lock                  # Lock file for exact dependency versions
└── README.md                    # This file
```

## Usage

### API Endpoints

#### Internal API (`/internal/api/v1.0`)
```bash
# Extract CV data from uploaded file
POST /internal/api/v1.0/extract
Content-Type: multipart/form-data
Body: file (PDF/DOC/DOCX)

# Retrieve processed document by ID
GET /internal/api/v1.0/extract/{doc_id}
```

#### Public API (`/public/api/v1.0`)
Public endpoints available for external integration (endpoints defined in `extract_public_router`)

#### Health Monitoring
```bash
# Check service health
GET /health
```

### Example Usage

**Upload and process a CV:**
```bash
curl -X POST "http://localhost:8080/internal/api/v1.0/extract" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@resume.pdf"
```

**Response:**
```json
{
  "code": 200,
  "error": null,
  "data": {
    "basic": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1-************"
    },
    "experience": [...],
    "education": [...],
    "skills": [...]
  },
  "results": {
    "es_id": "abc123def456"
  },
  "metadata": {
    "trace_id": "uuid-trace-id"
  }
}
```

**Retrieve processed document:**
```bash
curl "http://localhost:8080/internal/api/v1.0/extract/abc123def456"
```

### Data Structure

The extracted CV data follows a standardized schema with these main sections:

- **Basic Information** (`basic`): Name, email, phone, address, summary
- **Work Experience** (`experience`): Job history, companies, roles, responsibilities, dates
- **Education** (`education`): Degrees, institutions, graduation dates, GPA
- **Skills** (`skills`): Technical and soft skills with proficiency levels
- **Certifications** (`certifications`): Professional certifications and credentials
- **Languages** (`languages`): Language skills with proficiency ratings
- **Achievements** (`achievements`): Awards, recognitions, accomplishments
- **References** (`references`): Professional references with contact information
- **Extra-curricular** (`extra_curricular`): Activities, volunteer work, hobbies
- **External Documents** (`external_documents`): Portfolio links, websites, social profiles

### Async Processing Workflow

For background processing via Kafka:

1. **Message Consumption**: Service listens to configured Kafka topic
2. **File Download**: Retrieves CV files from S3-compatible storage
3. **Document Processing**: Extracts text and PII information
4. **LLM Analysis**: Processes content through AI models
5. **Data Storage**: Indexes structured data in Elasticsearch
6. **Result Notification**: Publishes completion message to result topic

## Development

### Running in Development Mode

```bash
# Set environment to development
export ENVIRONMENT=dev

# Run with auto-reload
poetry run python main.py
```

In development mode:
- CORS is enabled for all origins
- Root URL redirects to `/docs`
- Auto-reload is enabled
- Debug logging is more verbose

### Adding New Dependencies

```bash
# Add a new dependency
poetry add package-name

# Add a development dependency
poetry add --group dev package-name
```

## Environment Variables

### Core Configuration
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `ENVIRONMENT` | Application environment (dev/prod) | `dev` | No |
| `APP_HOST` | Host to bind the application | `0.0.0.0` | No |
| `APP_PORT` | Port to run the application | `8080` | No |
| `APP_WORKERS` | Number of worker processes | `1` | No |

### AI/LLM Configuration
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `OPENAI_API_KEY` | OpenAI API key for LLM processing | - | **Yes** |
| `OPENAI_MODEL` | OpenAI model to use | `gpt-4o-mini` | No |

### Database & Storage
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `ES_HOST` | Elasticsearch host | `localhost` | **Yes** |
| `ES_PORT` | Elasticsearch port | `9200` | No |
| `ES_INDEX` | Elasticsearch index name | `x_cv_extractor` | No |
| `ES_USER` | Elasticsearch username | - | No |
| `ES_PASSWORD` | Elasticsearch password | - | No |
| `REDIS_URL` | Redis connection URL for caching | - | No |
| `S3_DOMAIN` | S3-compatible storage domain | - | No |

### Message Queue (Async Processing)
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `BROKER_URL` | Kafka broker address | - | No |
| `CONSUMER_TOPIC` | Kafka topic to consume from | - | No |
| `PRODUCER_TOPIC` | Kafka topic to produce to | - | No |

### Monitoring & Observability
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SENTRY_DSN` | Sentry DSN for error tracking | - | No |
| `LANGFUSE_PUBLIC_KEY` | Langfuse public key for LLM tracking | - | No |
| `LANGFUSE_SECRET_KEY` | Langfuse secret key for LLM tracking | - | No |

## Load Testing

The project includes comprehensive load testing capabilities using [Locust](https://locust.io/) to test the CV extraction API under various load conditions.

### Prerequisites for Load Testing

1. **Install development dependencies** (includes Locust):
   ```bash
   poetry install --with dev
   ```

2. **Ensure the API is running**:
   ```bash
   poetry run python main.py
   ```

3. **Verify required services are available**:
   - Elasticsearch should be running
   - OpenAI API key should be configured
   - All environment variables should be set

### Running Load Tests

#### Quick Start

Run a basic load test with default settings:
```bash
# Using the test runner script
poetry run python tests/run_load_tests.py

# Or directly with Locust
poetry run locust -f tests/locustfile.py --host=http://localhost:8080
```

#### Test Scenarios

**Light Load Testing** (recommended for development):
```bash
poetry run python tests/run_load_tests.py --scenario=light --users=5 --spawn-rate=1 --run-time=2m
```

**Heavy Load Testing** (for performance validation):
```bash
poetry run python tests/run_load_tests.py --scenario=heavy --users=50 --spawn-rate=5 --run-time=10m
```

**Headless Mode** (for CI/CD integration):
```bash
poetry run python tests/run_load_tests.py --headless --users=20 --spawn-rate=3 --run-time=5m
```

#### Custom Configuration

You can also use the Locust configuration file:
```bash
poetry run locust -f tests/locustfile.py --config=tests/locust.conf
```

### Test Coverage

The load tests cover the following scenarios:

1. **Primary Operations** (80% of traffic):
   - CV file upload and processing
   - Various file content variations
   - Success and error response handling

2. **Secondary Operations** (20% of traffic):
   - Document retrieval by ID
   - Health check monitoring

3. **Edge Cases** (10% of traffic):
   - Invalid file types
   - Empty files
   - Oversized files
   - Error condition testing

### Test Results

After running load tests, you can find results in:

- **HTML Reports**: `tests/reports/load_test_*.html` - Interactive dashboard with metrics
- **CSV Data**: `tests/reports/load_test_*.csv` - Raw performance data
- **Logs**: `tests/logs/locust_*.log` - Detailed execution logs

### Load Testing Metrics

Key metrics to monitor during load testing:

- **Request Rate**: Requests per second (RPS)
- **Response Time**: Average, median, 95th percentile
- **Error Rate**: Percentage of failed requests
- **Throughput**: Data processed per second
- **Resource Usage**: CPU, memory, disk I/O

### Performance Benchmarks

Expected performance baselines:

| Metric | Light Load (5 users) | Heavy Load (50 users) |
|--------|---------------------|----------------------|
| Response Time (avg) | < 2s | < 5s |
| Error Rate | < 1% | < 5% |
| RPS | 2-5 | 10-20 |

### Troubleshooting Load Tests

**Common Issues**:

1. **API not responding**: Ensure the application is running on the correct port
   ```bash
   curl http://localhost:8080/health
   ```

2. **Elasticsearch connection errors**: Verify Elasticsearch is running and accessible
   ```bash
   curl http://localhost:9200/_cluster/health
   ```

3. **OpenAI API rate limits**: Monitor API usage and consider using test mode or mock responses

4. **File upload errors**: Check file permissions and temporary directory access

**Debug Mode**:
```bash
poetry run python tests/run_load_tests.py --users=1 --spawn-rate=1 --run-time=30s
```

### CI/CD Integration

For automated testing in CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Run Load Tests
  run: |
    poetry run python main.py &
    sleep 10  # Wait for API to start
    poetry run python tests/run_load_tests.py --headless --users=10 --run-time=2m
    pkill -f "python main.py"  # Stop API
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/your-feature`)
3. Make your changes
4. Run tests and ensure code quality
5. Commit your changes (`git commit -am 'Add some feature'`)
6. Push to the branch (`git push origin feature/your-feature`)
7. Create a new Pull Request

## License

This project is proprietary software developed by Navigos Group.
