[tool.poetry]
name = "x-cv-extractor"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
sentry-sdk = "^2.20.0"
uvicorn = "^0.34.0"
python-dotenv = "^1.0.1"
fastapi = "^0.115.7"
elasticsearch = {version = "^8.17.1", extras = ["async"]}
openai = "^1.60.1"
aiofiles = "^24.1.0"
requests = "^2.32.3"
httpx = "^0.27.0"
python-multipart = "^0.0.20"
langchain-openai = "^0.3.4"
langfuse = "^2.59.1"
llama-index = "^0.12.16"
langchain = "^0.3.18"
kafka-python = "^2.0.3"
aiokafka = "^0.11.0"
python-snappy = "^0.7.3"
phonenumbers = "^9.0.6"
redis = "^5.0.8"


[tool.poetry.group.dev.dependencies]
locust = "^2.37.10"
pre-commit = "^4.2.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
