import os

from locust import HttpUser, between, task


class APIStressTest(HttpUser):
    # Wait time between requests (1-3 seconds)
    wait_time = between(1, 3)

    @task(1)  # Weight 1 - runs least often
    def create_user(self):
        """Test POST endpoint"""
        pdf_path = "tests/fixtures/Hai.pdf"

        # Check if file exists
        if not os.path.exists(pdf_path):
            print(f"File not found: {pdf_path}")
            return

        # Open and upload the PDF file
        with open(pdf_path, "rb") as pdf_file:
            files = {"file": ("Hai.pdf", pdf_file, "application/pdf")}

            self.client.post("/internal/api/v1.0/extract", files=files)
