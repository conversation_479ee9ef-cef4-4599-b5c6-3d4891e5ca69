import logging
import os
import sentry_sdk
import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse

from app.api.v1.routers import (
    health_router,
    extract_internal_router,
    extract_public_router,
)

load_dotenv()

environment = os.getenv("ENVIRONMENT", "dev")


sentry_sdk.init(
    dsn=os.getenv("SENTRY_DSN", ""),
    traces_sample_rate=0.1,
)


app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


if environment == "dev":
    logger = logging.getLogger("uvicorn")
    logger.warning("Running in development mode - allowing CORS for all origins")

    # Redirect to documentation page when accessing base URL
    @app.get("/")
    async def redirect_to_docs():
        return RedirectResponse(url="/docs")


app.include_router(health_router)
app.include_router(extract_public_router)
app.include_router(extract_internal_router)


if __name__ == "__main__":
    app_host = os.getenv("APP_HOST", "0.0.0.0")
    app_port = int(os.getenv("APP_PORT", "8080"))
    workers = int(os.getenv("APP_WORKERS", "1"))
    reload = True if environment == "dev" else False
    uvicorn.run(
        app="main:app", host=app_host, port=app_port, reload=reload, workers=workers
    )
