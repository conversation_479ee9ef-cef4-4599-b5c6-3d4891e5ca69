import os
import traceback
import uuid

import phonenumbers
import sentry_sdk
from dotenv import load_dotenv
from elasticsearch import AsyncElasticsearch
from fastapi import APIRouter, File, HTTPException, UploadFile, status
from fastapi.responses import JSONResponse

# -- Your existing imports --
from app.handlers.file_processing import get_file_content, process_file_content
from app.handlers.llm import openai_resume_parser
from app.handlers.utils import normalize_address

load_dotenv()

# Create your ES client (Update host/port or auth details if needed)
ES_HOST = os.getenv("ES_HOST", "localhost")
ES_PORT = os.getenv("ES_PORT", "9200")
ES_INDEX = os.getenv("ES_INDEX", "x_cv_extractor")
ES_USER = os.getenv("ES_USER", "")
ES_PASSWORD = os.getenv("ES_PASSWORD", "")
es = AsyncElasticsearch(hosts=[f"http://{ES_USER}:{ES_PASSWORD}@{ES_HOST}:{ES_PORT}"])
extract_internal_router = r_internal = APIRouter(prefix="/internal/api/v1.0")
extract_public_router = r_public = APIRouter(prefix="/public/api/v1.0")


@r_internal.post("/extract")
async def extract(file: UploadFile = File(None)):
    """
    1. Processes an uploaded file
    2. Stores its parsed content in Elasticsearch
    3. Returns the ES _id in the response
    """
    if file.size == 0:
        raise HTTPException(status_code=404, detail={"message": "File is required"})
    if file.size > 5 * 1024 * 1024:
        raise HTTPException(status_code=413, detail={"message": "File is too large"})
    try:
        trace_id = str(uuid.uuid4())

        # -- Your existing file content retrieval code --
        content, extension, file_name_temp = await get_file_content(file)
        if extension not in ["pdf", "docx", "doc"]:
            return JSONResponse(
                content={
                    "code": status.HTTP_417_EXPECTATION_FAILED,
                    "error": "Resume's extension is invalid",
                },
                status_code=status.HTTP_417_EXPECTATION_FAILED,
            )

        if content is None:
            raise HTTPException(
                status_code=404, detail={"message": "Resume or file not found"}
            )

        content_object = await process_file_content(file_name_temp)
        pii_object = content_object.get("pii", {})
        text_content = content_object.get("text", "")

        if len(text_content) == 0:
            raise HTTPException(status_code=400, detail={"message": "File no content"})

        # -- Your existing LLM parsing code --
        response = await openai_resume_parser(text_content)

        # Example of handling phone from PII
        if pii_object.get("phone_number") and len(pii_object["phone_number"]) > 0:
            phone_number = pii_object["phone_number"][0]
            try:
                phone_number_parsed = phonenumbers.parse(
                    number=phone_number, region="VN"
                )
                if phonenumbers.is_valid_number(phone_number_parsed):
                    response.basic.phone = f"+{phone_number_parsed.country_code}-{phone_number_parsed.national_number}"
            except Exception as e:
                print(e)
                sentry_sdk.capture_exception(e)
                response.basic.phone = phone_number

        if (
            len(response.reference) > 0
            and pii_object.get("email_address")
            and len(pii_object["email_address"]) > 1
        ):
            for index in range(len(response.reference)):
                response.reference[index].email = (
                    pii_object["email_address"][index + 1]
                    if index + 1 < len(pii_object["email_address"])
                    and pii_object["email_address"][index + 1]
                    else ""
                )

        if (
            len(response.reference) > 0
            and pii_object.get("phone_number")
            and len(pii_object["phone_number"]) > 1
        ):
            for index in range(len(response.reference)):
                response.reference[index].phone = (
                    pii_object["phone_number"][index + 1]
                    if index + 1 < len(pii_object["phone_number"])
                    and pii_object["phone_number"][index + 1]
                    else ""
                )

        current_city = response.basic.current_city

        if current_city and isinstance(current_city, str) and len(current_city) > 0:
            response.basic.current_city = normalize_address(current_city)

        # Index the parsed response into Elasticsearch
        es_result = await es.index(index=ES_INDEX, document=response.dict())
        es_id = es_result["_id"]

        return JSONResponse(
            content={
                "code": status.HTTP_200_OK,
                "error": None,
                "data": response.model_dump(mode="json"),
                "results": {"es_id": es_id},
                "metadata": {"trace_id": trace_id},
            },
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        print(traceback.format_exc())
        sentry_sdk.capture_exception(e)
        return JSONResponse(
            content={"code": status.HTTP_500_INTERNAL_SERVER_ERROR, "error": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@r_internal.get("/extract/{doc_id}")
async def get_extracted_doc(doc_id: str):
    """
    Retrieve a stored document from Elasticsearch by its _id.
    """
    try:
        doc = await es.get(index=ES_INDEX, id=doc_id)

        # If doc is not found in Elasticsearch
        if not doc.get("found", False):
            raise HTTPException(status_code=404, detail="Document not found")

        # doc['_source'] holds the original object you stored
        return JSONResponse(content=doc["_source"], status_code=status.HTTP_200_OK)
    except Exception as e:
        print(traceback.format_exc())
        sentry_sdk.capture_exception(e)
        return JSONResponse(
            content={"code": status.HTTP_500_INTERNAL_SERVER_ERROR, "error": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
