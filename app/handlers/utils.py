import re
import unicodedata


def remove_accents(text: str) -> str:
    """Remove accents from Vietnamese text."""
    if not text:
        return text

    # Vietnamese-specific replacements for better accent removal
    vietnamese_chars = {
        "à": "a",
        "á": "a",
        "ạ": "a",
        "ả": "a",
        "ã": "a",
        "â": "a",
        "ầ": "a",
        "ấ": "a",
        "ậ": "a",
        "ẩ": "a",
        "ẫ": "a",
        "ă": "a",
        "ằ": "a",
        "ắ": "a",
        "ặ": "a",
        "ẳ": "a",
        "ẵ": "a",
        "è": "e",
        "é": "e",
        "ẹ": "e",
        "ẻ": "e",
        "ẽ": "e",
        "ê": "e",
        "ề": "e",
        "ế": "e",
        "ệ": "e",
        "ể": "e",
        "ễ": "e",
        "ì": "i",
        "í": "i",
        "ị": "i",
        "ỉ": "i",
        "ĩ": "i",
        "ò": "o",
        "ó": "o",
        "ọ": "o",
        "ỏ": "o",
        "õ": "o",
        "ô": "o",
        "ồ": "o",
        "ố": "o",
        "ộ": "o",
        "ổ": "o",
        "ỗ": "o",
        "ơ": "o",
        "ờ": "o",
        "ớ": "o",
        "ợ": "o",
        "ở": "o",
        "ỡ": "o",
        "ù": "u",
        "ú": "u",
        "ụ": "u",
        "ủ": "u",
        "ũ": "u",
        "ư": "u",
        "ừ": "u",
        "ứ": "u",
        "ự": "u",
        "ử": "u",
        "ữ": "u",
        "ỳ": "y",
        "ý": "y",
        "ỵ": "y",
        "ỷ": "y",
        "ỹ": "y",
        "đ": "d",
        # Uppercase
        "À": "A",
        "Á": "A",
        "Ạ": "A",
        "Ả": "A",
        "Ã": "A",
        "Â": "A",
        "Ầ": "A",
        "Ấ": "A",
        "Ậ": "A",
        "Ẩ": "A",
        "Ẫ": "A",
        "Ă": "A",
        "Ằ": "A",
        "Ắ": "A",
        "Ặ": "A",
        "Ẳ": "A",
        "Ẵ": "A",
        "È": "E",
        "É": "E",
        "Ẹ": "E",
        "Ẻ": "E",
        "Ẽ": "E",
        "Ê": "E",
        "Ề": "E",
        "Ế": "E",
        "Ệ": "E",
        "Ể": "E",
        "Ễ": "E",
        "Ì": "I",
        "Í": "I",
        "Ị": "I",
        "Ỉ": "I",
        "Ĩ": "I",
        "Ò": "O",
        "Ó": "O",
        "Ọ": "O",
        "Ỏ": "O",
        "Õ": "O",
        "Ô": "O",
        "Ồ": "O",
        "Ố": "O",
        "Ộ": "O",
        "Ổ": "O",
        "Ỗ": "O",
        "Ơ": "O",
        "Ờ": "O",
        "Ớ": "O",
        "Ợ": "O",
        "Ở": "O",
        "Ỡ": "O",
        "Ù": "U",
        "Ú": "U",
        "Ụ": "U",
        "Ủ": "U",
        "Ũ": "U",
        "Ư": "U",
        "Ừ": "U",
        "Ứ": "U",
        "Ự": "U",
        "Ử": "U",
        "Ữ": "U",
        "Ỳ": "Y",
        "Ý": "Y",
        "Ỵ": "Y",
        "Ỷ": "Y",
        "Ỹ": "Y",
        "Đ": "D",
    }

    # First, use the mapping for Vietnamese-specific characters
    for vietnamese_char, latin_char in vietnamese_chars.items():
        text = text.replace(vietnamese_char, latin_char)

    # Then use unicodedata for any remaining accents
    text = unicodedata.normalize("NFKD", text)
    return "".join([c for c in text if not unicodedata.combining(c)])


def expand_abbreviations(text: str) -> str:
    """Expand common Vietnamese location abbreviations."""
    if not text:
        return text

    # Common abbreviations mapping
    abbreviations = {
        # Cities
        r"\bHCM\b": "Ho Chi Minh",
        r"\bTP\. *HCM\b": "Ho Chi Minh",
        r"\bTP *HCM\b": "Ho Chi Minh",
        r"\bTp\. *HCM\b": "Ho Chi Minh",
        r"\bTp *HCM\b": "Ho Chi Minh",
        r"\bTP\. *Hồ Chí Minh\b": "Ho Chi Minh",
        r"\bTP *Hồ Chí Minh\b": "Ho Chi Minh",
        r"\bThành phố Hồ Chí Minh\b": "Ho Chi Minh",
        r"\bThanh pho Ho Chi Minh\b": "Ho Chi Minh",
        r"\bSG\b": "Ho Chi Minh",
        r"\bSài Gòn\b": "Ho Chi Minh",
        r"\bSai Gon\b": "Ho Chi Minh",
        r"\bHN\b": "Ha Noi",
        r"\bHà Nội\b": "Ha Noi",
        r"\bTP\. *HN\b": "Ha Noi",
        r"\bThành phố Hà Nội\b": "Ha Noi",
        r"\bThanh pho Ha Noi\b": "Ha Noi",
        r"\bDN\b": "Da Nang",
        r"\bĐà Nẵng\b": "Da Nang",
        r"\bTP\. *DN\b": "Da Nang",
        r"\bTP *DN\b": "Da Nang",
        r"\bThành phố Đà Nẵng\b": "Da Nang",
        r"\bThanh pho Da Nang\b": "Da Nang",
        r"\bHP\b": "Hai Phong",
        r"\bHải Phòng\b": "Hai Phong",
        r"\bTP\. *HP\b": "Hai Phong",
        r"\bThành phố Hải Phòng\b": "Hai Phong",
        r"\bThanh pho Hai Phong\b": "Hai Phong",
        r"\bCT\b": "Can Tho",
        r"\bCần Thơ\b": "Can Tho",
        r"\bTP\. *CT\b": "Can Tho",
        r"\bThành phố Cần Thơ\b": "Can Tho",
        r"\bThanh pho Can Tho\b": "Can Tho",
        # Provinces
        r"\bBT\b": "Ben Tre",
        r"\bBến Tre\b": "Ben Tre",
        r"\bVT\b": "Vung Tau",
        r"\bVũng Tàu\b": "Vung Tau",
        r"\bBR-VT\b": "Ba Ria - Vung Tau",
        r"\bBà Rịa-Vũng Tàu\b": "Ba Ria - Vung Tau",
        r"\bBà Rịa Vũng Tàu\b": "Ba Ria - Vung Tau",
        r"\bBa Ria Vung Tau\b": "Ba Ria - Vung Tau",
        r"\bKG\b": "Kien Giang",
        r"\bKiên Giang\b": "Kien Giang",
        r"\bBD\b": "Binh Duong",
        r"\bBình Dương\b": "Binh Duong",
        r"\bBH\b": "Binh Thuan",
        r"\bBình Thuận\b": "Binh Thuan",
        r"\bLA\b": "Long An",
        r"\bLong An\b": "Long An",
        r"\bTG\b": "Tien Giang",
        r"\bTiền Giang\b": "Tien Giang",
        r"\bĐN\b": "Dong Nai",
        r"\bĐồng Nai\b": "Dong Nai",
    }

    result = text
    for pattern, replacement in abbreviations.items():
        result = re.sub(pattern, replacement, result, flags=re.IGNORECASE)

    return result


def normalize_address(city: str) -> str:
    if city:
        normalized_city = expand_abbreviations(remove_accents(city))
    else:
        return None

    return normalized_city
