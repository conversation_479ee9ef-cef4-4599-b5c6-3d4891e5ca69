import json
import os
import re
from typing import Any, Optional

import sentry_sdk
from dotenv import load_dotenv
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import <PERSON>nableParallel
from langchain_openai import Chat<PERSON>penAI
from langfuse.callback import Callback<PERSON>andler

from app.handlers.prompts import prompts
from app.handlers.redis_cache import redis_cache
from app.models.user_profile import (
    Prompt1,
    Prompt2,
    Prompt3,
    Prompt4,
    Prompt5,
    UserProfile,
)

load_dotenv()

langfuse_handler = CallbackHandler()


async def _openai_resume_parser_uncached(content: str) -> Optional[Any]:
    model_name = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    model = ChatOpenAI(model=model_name, temperature=0)

    prompts_mapping = {
        "prompt_1": {
            "name": "PROMPT_1",
            "parser": PydanticOutputParser(pydantic_object=Prompt1),
        },
        "prompt_2": {
            "name": "PROMPT_2",
            "parser": PydanticOutputParser(pydantic_object=Prompt2),
        },
        "prompt_3": {
            "name": "PROMPT_3",
            "parser": PydanticOutputParser(pydantic_object=Prompt3),
        },
        "prompt_4": {
            "name": "PROMPT_4",
            "parser": PydanticOutputParser(pydantic_object=Prompt4),
        },
        "prompt_5": {
            "name": "PROMPT_5",
            "parser": PydanticOutputParser(pydantic_object=Prompt5),
        },
    }

    prompt_chains = {
        key: ChatPromptTemplate.from_template(template=prompts[value["name"]]).partial(
            schema=value["parser"].get_format_instructions(),
        )
        | model
        for key, value in prompts_mapping.items()
    }

    runnable = RunnableParallel(**prompt_chains)

    output = await runnable.ainvoke(
        {"content": content},
        config={"callbacks": [langfuse_handler], "run_name": "X CV Extractor"},
    )

    data = {}
    parser = PydanticOutputParser(pydantic_object=UserProfile)

    for value in output.values():
        try:
            data_block = json.loads(clean_json_string(value.content))
        except Exception:
            try:
                json_blocks = extract_code_blocks(content=value.content)
                data_block = json.loads(json_blocks)
            except Exception as error:
                import traceback

                print(traceback.format_exc())
                sentry_sdk.capture_exception(error)
                return None, None
        data.update(data_block)

    try:
        parsed_response = await parser.ainvoke(json.dumps(data))
    except Exception as e:
        print(e)
        from langchain.output_parsers import OutputFixingParser

        retry_parser = OutputFixingParser.from_llm(
            llm=ChatOpenAI(
                model="gpt-4o",
                temperature=0,
                cache=False,
                timeout=60,
            ),
            max_retries=2,
            parser=parser,
        )

        parsed_response = await retry_parser.ainvoke(json.dumps(data))
    value = parsed_response

    return value


async def openai_resume_parser(content: str) -> Optional[Any]:
    if not content or len(content.strip()) == 0:
        return None

    cached_result = None
    try:
        cache_key = redis_cache.generate_cache_key(content)
        cached_result = await redis_cache.get_cached_result(cache_key)
        if cached_result is not None:
            print("Cache hit - returning cached result")
            return cached_result
    except Exception as e:
        print(f"Cache retrieval failed, proceeding without cache: {e}")

    try:
        result = await _openai_resume_parser_uncached(content)

        if result is not None:
            try:
                cache_key = redis_cache.generate_cache_key(content)
                success = await redis_cache.set_cached_result(cache_key, result)
                if success:
                    print("Result cached successfully")
            except Exception as e:
                print(f"Cache storage failed, continuing: {e}")

        return result
    except Exception as e:
        sentry_sdk.capture_exception(e)
        return None


def clean_json_string(json_str: str) -> str:
    return (
        json_str.replace(", ...", "")
        .replace("...", "")
        .replace(
            """,
                        },""",
            "},",
        )
        .replace("```json", "")
        .replace("```", "")
        .replace("yyyy/01", "")
        .replace("Unknown", "")
        .replace("YYYY", "")
        .replace("N/A", "")
    )


def extract_code_blocks(content: str) -> str:
    pattern = re.compile(r"```json(.*?)```", re.DOTALL)
    pattern_result = pattern.findall(content)
    if len(pattern_result) > 0:
        return pattern_result[0]
    return ""
