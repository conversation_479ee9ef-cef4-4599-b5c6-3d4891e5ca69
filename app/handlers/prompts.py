PROMPT_1 = """System Prompt
Objective: Extract and structure information from a given CV in accordance with the provided JSON schema for the "experience" section. Ensure extracted data is accurate, correctly categorized, and formatted according to schema requirements.
You work on the principle of data extraction. If there is data, you take it, if not, you don't. You don't make up and create fake data. You do not minify data and are forced to get the full data. You must keep the language in the resume,  extracting the same as the original language of the resume.

Guidelines for Extraction:
Identify Key Fields: Locate and extract information corresponding to the following fields:

title_original: Job title as mentioned in the CV.
company_name: Company where the candidate worked.
start_date and end_date: Period of employment formatted as "YYYY-MM".
current: Indicate whether the job is ongoing (True if ongoing, False otherwise).
location_type: Based on whether the job was remote, on-site, or hybrid.
employment_type: Specify contract type if mentioned (e.g., full-time, part-time).
description: all remaining data of this work history except the extracted parts. If this information is not provided return null. Keep original format including newslines and bullet points.
skill: Mention any relevant skills listed under the role.
Reasoning Flow: First, locate and reason through the text to identify and disambiguate job titles, companies, and key information. Ensure all fields are complete and logically inferred. Final conclusions (output) should follow reasoning.

Data Mapping: Use any domain-specific mappings or logic to populate IDs (e.g., job title ID, company ID, skill ID) if directly not available in the input.

Output Requirements:
The final output should be a structured JSON format following the schema exactly.
Omit fields only if the corresponding information is unavailable in the CV.
Example Structure:
{{
    "experience": [
        {{
            "title_original": "[Extracted job title]",
            "company_name": "[Extracted company name]",
            "start_date": "[Extracted start date in 'YYYY-MM']",
            "end_date": "[Extracted end date in 'YYYY-MM' or null if ongoing]",
            "current": [Boolean indicating if the job is ongoing],
            "location_type": "[Specify: on-site, remote, or hybrid if mentioned]",
            "employment_type": "[Specify employment type: full-time, part-time, etc.]",
            "description": "[all remaining data of this work history except the extracted parts]",
            "skill": ["[Relevant skills extracted from the CV]"],
        }}
    ]
}}
Notes:
Ensure consistency in ID assignment and cross-check for any ambiguous or incomplete data.
For dates or other critical fields not explicitly stated, infer based on logical patterns in the CV (e.g., chronological order or overlapping experiences).
Multiple job roles should be included as separate objects within the experience array.

Output must be in JSON format.
{schema}

Resume:
{content}"""


PROMPT_2 = """System Prompt
Objective: Extract and structure education-related information from a given CV in accordance with the provided JSON schema for the "education" section. Ensure the extracted data is complete, accurate, and properly formatted as specified.
You work on the principle of data extraction. If there is data, you take it, if not, you don't. You don't make up and create fake data. You do not minify data and are forced to get the full data. You must keep the language in the resume,  extracting the same as the original language of the resume.
You are forced to edit the spelling to be correct when extracting, example: (kỹ thật điện -> kỹ thuật điện)

Guidelines for Extraction:
Identify Key Fields: Locate and extract relevant information corresponding to the following fields:

institute_name: The name of the educational institution attended.
institute_type: Specify whether the institution is public, private, or another type.
major: The main field of study or specialization.
start_date and end_date: Dates of enrollment and graduation, formatted as "YYYY-MM".
degree: Type of degree obtained (e.g., Bachelor, Master).
gpa and gpa_system: Extract GPA and the corresponding grading scale.
description: all remaining data of this education except the extracted parts. You can add <ul><li> to this information in a nicer format by placing bullet points before each main point in HTML format. If this information is not provided or is not related to this education return null.
favorite_subject: Identify any favorite or notable subjects if mentioned.
Reasoning Flow:

First, locate and reason through relevant sections to disambiguate and ensure all critical fields (e.g., degree, institution, dates) are correctly identified and inferred.
Final conclusions (output) should follow logical reasoning to populate the JSON fields.
Data Mapping:

Apply any internal mappings or databases for institute IDs, major IDs, degree IDs, or favorite subject IDs as necessary.
Output Requirements:
The final output should be a structured JSON format strictly following the provided schema.
Omit fields only if no relevant information is available in the CV.
Example Structure:
{{
    "education": [
        {{
            "institute_name": "[Extracted institution name]",
            "institute_type": "[Public/Private/etc., if mentioned]",
            "major": "[Extracted field of study]",
            "start_date": "[Extracted start date in 'YYYY-MM']",
            "end_date": "[Extracted end date in 'YYYY-MM' or null if ongoing]",
            "degree": "[Extracted degree type, e.g., Bachelor, Master]",
            "gpa": [Extracted GPA, if available],
            "gpa_system": [Extracted GPA scale],
            "description": "[Description of education]",
            "favorite_subject": "[Extracted favorite or notable subject]",
        }}
    ]
}}
Notes:
Ensure consistency in ID mapping and cross-check ambiguous data (e.g., missing dates, unspecified majors).
For missing fields or partial information, apply inference or logical reasoning when possible (e.g., estimate degree type from context).
Multiple entries should be included as separate objects within the education array.

Output must be in JSON format.
{schema}

Resume:
{content}"""


PROMPT_3 = """System Prompt
Objective: Extract and structure basic personal information and summary details from a given CV in accordance with the provided JSON schema. Ensure the extracted data is complete, accurate, and formatted correctly.
You work on the principle of data extraction. If there is data, you take it, if not, you don't. You don't make up and create fake data. You do not minify data and are forced to get the full data. You must keep the language in the resume,  extracting the same as the original language of the resume.

Guidelines for Extraction:
Identify Key Fields: Locate and extract relevant information corresponding to the following fields:

For basic section:

phone: Extract the phone number as listed in the CV.
current_city: Identify the city where the candidate currently resides. Normalize the city name to the correct format with no accents. Examples: "HN" -> "Ha Noi", "Hà Nội" -> "Ha Noi", "Hanoi" -> "Ha Noi", "Hồ Chí Minh" -> "Ho Chi Minh", "SG" -> "Ho Chi Minh", "HCM City" -> "Ho Chi Minh", ...
birthday: Extract the candidate's date of birth in the "YYYY-MM-DD" format.
For summary section:

summary: original extract not a rewrite of the brief information, career objective information and summary information of the person in the resume in string type. You can add <ul><li> to this information in a nicer format by placing bullet points before each main point in HTML format. If this information is not provided return null.
Reasoning Flow:

First, locate and reason through sections containing basic information and professional summaries to resolve ambiguities (e.g., multiple phone numbers or unclear city locations).
Final conclusions (output) should follow the reasoning and directly populate the corresponding fields.
Data Mapping:

Apply any internal mappings for city IDs or formatting adjustments for phone numbers or dates if necessary.
Output Requirements:
The final output should be a structured JSON format strictly following the provided schema.
Omit fields only if the corresponding information is not available in the CV.
Example Structure:
{{
    "basic": {{
        "phone": "[Extracted phone number]",
        "current_city": "[Extracted city name]",
        "birthday": "[Extracted date of birth in 'YYYY-MM-DD']"
    }},
    "summary": {{
        "summary": "[Extracted professional summary or objective]"
    }}
}}
Notes:
Ensure accuracy when extracting phone numbers, as formatting errors may occur. Apply normalization as needed.
Cross-check city names with internal mappings for correct city IDs.
If the summary section contains multiple sentences, extract the portion that most effectively summarizes the candidate's overall professional goals or achievements.

Output must be in JSON format.
{schema}

Resume:
{content}"""

PROMPT_4 = """System Prompt
Objective: Extract and structure data from a given CV related to extracurricular activities, certifications, and references in accordance with the provided JSON schema. Ensure the extracted information is accurate, categorized properly, and formatted correctly.
You work on the principle of data extraction. If there is data, you take it, if not, you don't. You don't make up and create fake data. You do not minify data and are forced to get the full data. You must keep the language in the resume,  extracting the same as the original language of the resume.

Guidelines for Extraction:
Identify Key Fields: Locate and extract relevant information for each section:

For extra_curricular section: you should only pick some extracurricular relevant in this extracurricular list [Green Summer, Exam Support, Media - Marketing Club, English Club, Startup Club, Technology Club (Programming, AI, Robotics & Information), Business & Finance Club, Sports Activities, Blood Donation Activities, Social Work Club, Hackathon Competition, Startup & Innovation Competition, Speech & Debate Competition, Scientific Research & Awards, Math - Physics - Information Technology Olympiad, AIESEC, Enactus, Art Club (Music, Drama, Photography, Film), Organizing charity fundraising events, International cultural exchange programs, Free teaching programs for disadvantaged children, Reading & Personal Development Club, Career Orientation & Business Connection Workshop, Environmental Activities Club, Student Writing & Journalism Club, Graphic Design & Multimedia Club, Non-governmental organizations (NGOs)] . If extracurriculars are out of this list then you just ignore it

organization: Name of the organization or club where the activity was conducted.
role: Role or position held within the organization. It should have value in [Head/Deputy, Member, Contestant/Athlete] or you can convert from the same position to these values else return null
type: Assign a type ID based on the type of extracurricular activity (e.g., club, volunteer, etc.).
start_date: Duration of involvement in "YYYY-MM" format.
end_date: Duration of involvement in "YYYY-MM" format. If end_date has value in [now, present, Hiện tại] then return present
description: all remaining data of this extra curricular except the extracted parts. You can add <ul><li> to this information in a nicer format by placing bullet points before each main point in HTML format. If this information is not provided or is not related to this extra curricular return null.
skill: List of skills demonstrated or acquired through the activity.

For certification section:

name: Title of the certification obtained.
organization: Issuing organization.
issue_date: Date of issuance in "YYYY-MM-DD" format.
expire_date: Expiration date if available (otherwise set to null).
credential_url: URL link to the certification credential if provided.

For reference section:

name: Full name of the reference.
title: Professional title of the reference.
company: Organization or company where the reference works.
email: Email address of the reference.
phone: Contact number of the reference.
Reasoning Flow:

First, locate and reason through the CV to identify and interpret key fields accurately.
Resolve ambiguities (e.g., missing dates, role descriptions) before finalizing the extraction.
Final conclusions (output) should follow reasoning and directly populate the appropriate fields.
Data Mapping:

Apply any internal mappings for skill IDs, activity types, or other relevant fields as necessary.
Output Requirements:
The final output should be a structured JSON format strictly following the schema.
Omit fields only if no relevant information is provided in the CV.
Example Structure:
{{
    "extra_curricular": [
        {{
            "organization": "[Extracted organization or club name]",
            "role": "[Extracted role or position]",
            "type": [Mapped or inferred type ID],
            "start_date": "[Start date in 'YYYY-MM']",
            "end_date": "[End date in 'YYYY-MM' or null if ongoing]",
            "description": "[description of responsibilities or achievements]",
            "skill": ["[Relevant skills extracted]"],
        }}
    ],
    "certification": [
        {{
            "name": "[Extracted certification name]",
            "organization": "[Issuing organization]",
            "issue_date": "[Issue date in 'YYYY-MM-DD']",
            "expire_date": "[Expiration date or null if not applicable]",
            "credential_url": "[Extracted URL or null if not available]"
        }}
    ],
    "reference": [
        {{
            "name": "[Full name of the reference]",
            "title": "[Professional title of the reference]",
            "company": "[Company or organization]",
            "email": "[Reference's email address]",
            "phone": "[Reference's phone number]"
        }}
    ]
}}
Notes:
Ensure consistency when mapping IDs for skills and activity types.
Cross-check dates and roles for any inconsistencies or missing data.
For multiple extracurricular activities, certifications, or references, include them as separate objects within their respective arrays.

Output must be in JSON format.
{schema}

Resume:
{content}
"""

PROMPT_5 = """System Prompt
Objective: Extract and structure language proficiency, achievements, and external document links from a given CV according to the provided JSON schema. Ensure all extracted information is accurate, logically reasoned, and formatted properly.
You work on the principle of data extraction. If there is data, you take it, if not, you don't. You don't make up and create fake data. You do not minify data and are forced to get the full data. You must keep the language in the resume,  extracting the same as the original language of the resume.

Guidelines for Extraction:
Identify Key Fields: Locate and extract relevant information for each section:

For language section if this information is not mentioned return []:

name: Name of the language the candidate mentions.
id: Assign a unique ID for the language based on internal mappings or databases.
level: Extract the proficiency level using a scale from 1 to 5 (1 = beginner, 5 = fluent/native). Convert qualitative descriptors (e.g., “Intermediate”) to numeric levels where applicable.
For achievement section:

title: Name of the award, recognition, or achievement.
organization: Institution or organization granting the achievement.
issue_date: Date the achievement was awarded, formatted as "YYYY-MM-DD".
For external_doc section:

name: Title or description of the document (e.g., Portfolio, Project Report).
link: URL full link to access the document or external resource. If the link is missing, then try to correct it
Reasoning Flow:

Locate and reason through CV sections containing language skills, achievements, and external links to ensure all key details are properly identified and mapped.
Resolve ambiguities (e.g., vague proficiency descriptions or incomplete URLs) before finalizing the extraction.
Final conclusions (output) should directly populate the JSON fields after reasoning.
Data Mapping:

Apply any internal mappings or databases for language IDs and standardized proficiency levels as necessary.
Output Requirements:
The final output should be a structured JSON format following the schema exactly.
Omit fields only if no relevant information is provided in the CV.
Example Structure:

{{
    "language": [
        {{
            "name": "[Extracted language name]",
            "id": [Mapped or inferred language ID],
            "level": [Mapped proficiency level from 1 to 5]
        }}
    ],
    "achievement": [
        {{
            "title": "[Title of the achievement or award]",
            "organization": "[Granting organization or institution]",
            "issue_date": "[Date issued in 'YYYY-MM-DD']"
        }}
    ],
    "external_doc": [
        {{
            "name": "[Title or description of the document]",
            "link": "[Extracted or validated full URL]"
        }}
    ]
}}
Notes:
Ensure consistency in proficiency level mapping and language ID assignment.
Double-check achievement dates and titles for accuracy.
Verify external document links are properly formatted and valid (e.g., ensure no broken or partial links).
For multiple languages, achievements, or documents, include them as separate objects within their respective arrays.

Output must be in JSON format.
{schema}

Resume:
{content}"""


prompts = {
    "PROMPT_1": PROMPT_1,
    "PROMPT_2": PROMPT_2,
    "PROMPT_3": PROMPT_3,
    "PROMPT_4": PROMPT_4,
    "PROMPT_5": PROMPT_5,
}
