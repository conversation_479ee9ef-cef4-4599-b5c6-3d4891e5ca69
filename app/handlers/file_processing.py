import os
import tempfile
from base64 import b64encode

import aiofiles
import httpx


async def get_file_content(file):
    if file:
        return await save_uploaded_file(file)
    return None, None, None


async def save_uploaded_file(file):
    file_name = file.filename
    extension = file_name.split(".")[-1]

    # Create temporary file with proper extension
    temp_fd, temp_path = tempfile.mkstemp(suffix=f".{extension}")

    try:
        content = await file.read()
        # Write content to temporary file
        async with aiofiles.open(temp_path, "wb") as out_file:
            await out_file.write(content)

        return content, extension, temp_path
    finally:
        # Close the file descriptor
        os.close(temp_fd)


async def process_file_content(temp_file_path):
    return await reader_service(temp_file_path)


async def reader_service(file_path):
    url = os.getenv("TEXTRACT_SERVICE_URL")

    async with aiofiles.open(file_path, "rb") as open_file:
        byte_content = await open_file.read()
        base64_bytes = b64encode(byte_content)
        base64_string = base64_bytes.decode("utf-8")
        raw_data = {
            "data": base64_string,
            "file_type": file_path.split(".")[-1],
            "is_pii": True,
        }

    async with httpx.AsyncClient() as client:
        response = await client.post(f"{url}/internal", json=raw_data)
        if response.status_code == 200:
            return response.json()
        else:
            print(response)
            return {}
