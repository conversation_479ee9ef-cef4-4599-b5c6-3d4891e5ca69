import hashlib
import json
import os
from typing import Any, Optional

import redis.asyncio as redis
from dotenv import load_dotenv

load_dotenv()


class RedisCache:
    def __init__(self):
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.cache_enabled = os.getenv("CACHE_ENABLED", "true").lower() == "true"
        self.default_ttl = int(os.getenv("CACHE_TTL", "3600"))
        self.cache_prefix = os.getenv("CACHE_PREFIX", "x_cv_extractor")

        self._redis_client = None
        self._connection_failed = False

    async def create_redis_pool(self):
        """Create and return a Redis connection pool"""
        pool = redis.ConnectionPool.from_url(
            self.redis_url,
            max_connections=20,
            retry_on_timeout=True,
            socket_connect_timeout=5,
            socket_timeout=5,
        )
        return pool

    async def get_redis_client(self) -> Optional[redis.Redis]:
        if not self.cache_enabled or self._connection_failed:
            return None

        if self._redis_client is None:
            try:
                pool = await self.create_redis_pool()
                self._redis_client = redis.Redis.from_pool(pool)
                await self._redis_client.ping()
                self._connection_failed = False
            except Exception as e:
                print(f"Redis connection failed, disabling cache: {e}")
                self._redis_client = None
                self._connection_failed = True
                return None

        try:
            await self._redis_client.ping()
            return self._redis_client
        except Exception as e:
            print(f"Redis ping failed, resetting connection: {e}")
            self._redis_client = None
            self._connection_failed = True
            return None

    def generate_cache_key(
        self, content: str, function_name: str = "openai_resume_parser"
    ) -> str:
        content_hash = hashlib.sha256(content.encode("utf-8")).hexdigest()[:16]
        model_name = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
        return f"{self.cache_prefix}:{function_name}:{model_name}:{content_hash}"

    async def get_cached_result(self, cache_key: str) -> Optional[Any]:
        try:
            client = await self.get_redis_client()
            if not client:
                return None

            cached_data = await client.get(cache_key)
            if cached_data:
                from app.models.user_profile import UserProfile

                cached_dict = json.loads(cached_data)
                return UserProfile(**cached_dict)
        except Exception as e:
            print(f"Redis get error, continuing without cache: {e}")
            self._connection_failed = True

        return None

    async def set_cached_result(
        self, cache_key: str, result: Any, ttl: Optional[int] = None
    ) -> bool:
        try:
            client = await self.get_redis_client()
            if not client:
                return False

            ttl = ttl or self.default_ttl
            if hasattr(result, "model_dump"):
                serialized_result = json.dumps(result.model_dump())
            elif hasattr(result, "dict"):
                serialized_result = json.dumps(result.dict())
            else:
                serialized_result = json.dumps(result, default=str)
            await client.setex(cache_key, ttl, serialized_result)
            return True
        except Exception as e:
            print(f"Redis set error, continuing without cache: {e}")
            self._connection_failed = True
            return False

    async def close(self):
        if self._redis_client:
            await self._redis_client.close()


redis_cache = RedisCache()
