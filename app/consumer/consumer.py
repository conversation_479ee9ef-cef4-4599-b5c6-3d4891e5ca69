import asyncio
import json
import os
import traceback
from pathlib import Path

import aiofiles
import httpx
from elasticsearch import AsyncElasticsearch
from aiokafka import AIOKafkaConsumer, AIOKafkaProducer

from app.handlers.file_processing import process_file_content
from app.handlers.llm import openai_resume_parser

# -------------------------------------------------------------------
# 1. Reuse or import your existing ES logic (adjust to your path)
#    This might be the same code you have in your FastAPI app.
# -------------------------------------------------------------------

ES_HOST = os.getenv("ES_HOST", "localhost")
ES_PORT = os.getenv("ES_PORT", "9200")
ES_INDEX = os.getenv("ES_INDEX", "x_cv_extractor")
ES_USER = os.getenv("ES_USER", "")
ES_PASSWORD = os.getenv("ES_PASSWORD", "")

es = AsyncElasticsearch(hosts=[f"http://{ES_USER}:{ES_PASSWORD}@{ES_HOST}:{ES_PORT}"])


async def get_file_content_from_url(file_url: str):
    """
    Example stub that downloads a file and returns:
     - raw content
     - file extension (e.g. ".docx", ".pdf")
     - temporary saved filename
    """
    # Download file
    Path("uploads").mkdir(exist_ok=True)

    async with httpx.AsyncClient() as client:
        r = await client.get(file_url)
        if r.status_code != 200:
            raise ValueError(f"Unable to fetch file from URL: {file_url}")

        # For demonstration, we just store in memory or a temp file
        path = os.path.splitext(file_url)
        extension = path[1]  # e.g. .docx
        file_name = path[0].split("/")[-1]
        temp_file_name = file_name + extension
        print(temp_file_name)

        async with aiofiles.open("uploads/" + temp_file_name, "wb") as f:
            await f.write(r.content)

        return r.content, extension, temp_file_name


BROKER_URL = os.getenv("BROKER_URL", "")
CONSUMER_TOPIC = os.getenv("CONSUMER_TOPIC", "")
PRODUCER_TOPIC = os.getenv("PRODUCER_TOPIC", "")


# -------------------------------------------------------------------
# 5. Main loop: consume -> process -> produce
# -------------------------------------------------------------------


async def main():
    print(f"Starting Kafka Consumer on topic: {CONSUMER_TOPIC}")

    # Initialize consumer and producer with async context managers
    consumer = AIOKafkaConsumer(
        CONSUMER_TOPIC,
        bootstrap_servers=BROKER_URL,
        auto_offset_reset="earliest",
        enable_auto_commit=True,
        group_id="cv-parser-group",
        value_deserializer=lambda m: json.loads(m.decode("utf-8")),
    )

    producer = AIOKafkaProducer(
        bootstrap_servers=BROKER_URL,
        value_serializer=lambda m: json.dumps(m).encode("utf-8"),
    )

    try:
        # Start consumer and producer
        await consumer.start()
        await producer.start()

        print("Kafka consumer and producer started successfully")

        # Async iteration over messages
        async for message in consumer:
            # message.value is already deserialized by `value_deserializer`
            incoming_data = message.value
            print(f"Received message: {incoming_data}")

            try:
                # Example message format:
                # {
                #   "results": {
                #       "fileUrl": "https://...",
                #       "source": "Onboarding",
                #       "timetamp": "2025-02-18T15:50:18+07:00",
                #       "userId": "5591975"
                #   }
                # }
                results = incoming_data.get("results", {})
                file_url = results.get("fileUrl")
                user_id = results.get("userId")
                source = results.get("source")
                timestamp = results.get("timetamp")
                s3_domain = os.getenv("S3_DOMAIN", "")

                if not file_url:
                    raise ValueError("No fileUrl in message")

                # 1) Download file
                content, extension, temp_file_name = await get_file_content_from_url(
                    s3_domain + "/external/source/" + file_url
                )

                # 2) Parse file -> text + PII
                file_data = await process_file_content(temp_file_name)
                text_content = file_data.get("text", "")
                pii_object = file_data.get("pii", {})

                # 3) Use LLM to parse into structured object
                user_profile = await openai_resume_parser(text_content)

                if user_profile is None:
                    print(
                        f"Error: user_profile is None for user_id {user_id}, skipping message"
                    )
                    continue

                # 4) Set phone from PII if available
                phone_numbers = pii_object.get("phone_number")
                if phone_numbers and len(phone_numbers) > 0:
                    user_profile.basic.phone = phone_numbers[0]

                user_profile.upzi_user_id = user_id
                user_profile.source = source
                # 5) Index into Elasticsearch
                es_result = await es.index(
                    index=ES_INDEX, document=user_profile.model_dump(mode="json")
                )
                es_id = es_result["_id"]
                print(f"Indexed doc to ES with _id={es_id}")

                # 6) Produce a result message to `ai-parser-result`
                output_msg = {
                    "results": {
                        "retrieval_id": es_id,
                        "timestamp": timestamp,
                        "userId": user_id,
                        "source": source,
                    }
                }
                await producer.send_and_wait(PRODUCER_TOPIC, value=output_msg)

            except Exception as e:
                traceback.print_exc()
                # Handle/log the error as needed
                print(f"Error while processing message: {e}")

    except KeyboardInterrupt:
        print("Shutting down consumer...")
    finally:
        # Clean shutdown
        await consumer.stop()
        await producer.stop()


if __name__ == "__main__":
    asyncio.run(main())
