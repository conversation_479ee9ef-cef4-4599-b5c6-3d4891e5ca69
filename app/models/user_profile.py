from typing import List, Optional

from pydantic import BaseModel


class Basic(BaseModel):
    phone: Optional[str] = None
    current_city: Optional[str] = None
    birthday: Optional[str] = None


class Summary(BaseModel):
    summary: Optional[str] = None


class Education(BaseModel):
    institute_name: Optional[str] = None
    institute_type: Optional[str] = None
    major: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    degree: Optional[str] = None
    gpa: Optional[float] = None
    gpa_system: Optional[float] = None
    description: Optional[str] = None
    favorite_subject: Optional[str] = None


class Experience(BaseModel):
    title_original: Optional[str] = None
    company_name: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    current: Optional[bool] = None
    location_type: Optional[str] = None
    employment_type: Optional[str] = None
    description: Optional[str] = None
    skill: Optional[List[str]] = None


class ExtraCurricular(BaseModel):
    organization: Optional[str] = None
    role: Optional[str] = None
    type: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    description: Optional[str] = None
    skill: Optional[List[str]] = None


class Certification(BaseModel):
    name: Optional[str] = None
    organization: Optional[str] = None
    issue_date: Optional[str] = None
    expire_date: Optional[str] = None
    credential_url: Optional[str] = None


class Reference(BaseModel):
    name: Optional[str] = None
    title: Optional[str] = None
    company: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None


class Language(BaseModel):
    name: Optional[str] = None
    level: Optional[int] = None


class Achievement(BaseModel):
    title: Optional[str] = None
    organization: Optional[str] = None
    issue_date: Optional[str] = None


class ExternalDoc(BaseModel):
    name: Optional[str] = None
    link: Optional[str] = None


class UserProfile(BaseModel):
    basic: Optional[Basic] = None
    summary: Optional[Summary] = None
    education: Optional[List[Education]] = []
    experience: Optional[List[Experience]] = []
    extra_curricular: Optional[List[ExtraCurricular]] = []
    certification: Optional[List[Certification]] = []
    reference: Optional[List[Reference]] = []
    language: Optional[List[Language]] = []
    achievement: Optional[List[Achievement]] = []
    external_doc: Optional[List[ExternalDoc]] = []
    source: Optional[str] = None
    upzi_user_id: Optional[str] = None
    vnw_user_id: Optional[str] = None


class Prompt1(BaseModel):
    experience: List[Experience]


class Prompt2(BaseModel):
    education: List[Education]


class Prompt3(BaseModel):
    basic: Basic
    summary: Summary


class Prompt4(BaseModel):
    extra_curricular: List[ExtraCurricular]
    certification: List[Certification]
    reference: List[Reference]


class Prompt5(BaseModel):
    language: List[Language]
    achievement: List[Achievement]
    external_doc: List[ExternalDoc]
